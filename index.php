<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>آلة حاسبة ذكية متطورة</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(45deg, #0f0c29, #302b63, #24243e);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow-x: hidden;
        }

        /* خلفية متحركة */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="g"><stop offset="20%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="50%" stop-color="%23ffffff" stop-opacity="0.05"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="20" cy="20" r="2" fill="url(%23g)"><animate attributeName="cy" values="20;80;20" dur="3s" repeatCount="indefinite"/></circle><circle cx="50" cy="40" r="3" fill="url(%23g)"><animate attributeName="cy" values="40;10;40" dur="4s" repeatCount="indefinite"/></circle><circle cx="80" cy="60" r="2" fill="url(%23g)"><animate attributeName="cy" values="60;30;60" dur="3.5s" repeatCount="indefinite"/></circle></svg>');
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .calculator-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 30px;
            box-shadow: 
                0 20px 60px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            padding: 40px;
            width: 100%;
            max-width: 600px;
            position: relative;
            overflow: hidden;
        }

        .calculator-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
            animation: shine 6s linear infinite;
            pointer-events: none;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .calculator-header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            z-index: 2;
        }

        .calculator-header h1 {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 15px;
            animation: pulse 2s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            100% { transform: scale(1.05); }
        }

        .calculator-header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.3em;
            font-weight: 300;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.2em;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        input[type="number"], select {
            width: 100%;
            padding: 18px 25px;
            border: none;
            border-radius: 15px;
            font-size: 1.2em;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s ease;
            position: relative;
        }

        input[type="number"]::placeholder, select option {
            color: rgba(255, 255, 255, 0.6);
        }

        input[type="number"]:focus, select:focus {
            outline: none;
            transform: translateY(-2px);
            box-shadow: 
                0 10px 30px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
        }

        select {
            cursor: pointer;
            appearance: none;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="white" viewBox="0 0 24 24"><path d="M7 10l5 5 5-5z"/></svg>');
            background-repeat: no-repeat;
            background-position: left 15px center;
            background-size: 20px;
            padding-left: 50px;
        }

        select option {
            background: #2c3e50;
            color: white;
            padding: 10px;
        }

        .calculate-btn {
            width: 100%;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.3em;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s ease;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .calculate-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .calculate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }

        .calculate-btn:hover::before {
            left: 100%;
        }

        .calculate-btn:active {
            transform: translateY(-1px);
        }

        .result {
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(78, 205, 196, 0.2));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            text-align: center;
            animation: resultAppear 0.8s ease;
            position: relative;
            overflow: hidden;
        }

        .result::before {
            content: '✨';
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 2em;
            animation: sparkle 2s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.7; }
            50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
        }

        @keyframes resultAppear {
            from { 
                opacity: 0; 
                transform: translateY(30px) scale(0.9); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0) scale(1); 
            }
        }

        .result h3 {
            font-size: 2em;
            margin-bottom: 20px;
            color: white;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .result p {
            font-size: 1.4em;
            margin: 10px 0;
            color: rgba(255, 255, 255, 0.9);
        }

        .operation-display {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            font-size: 1.8em;
            font-weight: 700;
            color: #4ecdc4;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .number-type {
            background: linear-gradient(45deg, rgba(150, 206, 180, 0.3), rgba(69, 183, 209, 0.3));
            padding: 15px;
            border-radius: 15px;
            margin-top: 20px;
            font-weight: 600;
            font-size: 1.3em;
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            0% { box-shadow: 0 0 10px rgba(150, 206, 180, 0.5); }
            100% { box-shadow: 0 0 20px rgba(69, 183, 209, 0.8); }
        }

        .error {
            background: linear-gradient(45deg, rgba(255, 107, 107, 0.3), rgba(238, 90, 36, 0.3));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 107, 107, 0.5);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-top: 20px;
            text-align: center;
            font-weight: 600;
            font-size: 1.2em;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            color: white;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .feature-card .icon {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
        }

        @media (max-width: 768px) {
            .calculator-container {
                margin: 10px;
                padding: 25px;
            }
            
            .calculator-header h1 {
                font-size: 2.2em;
            }

            .features-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* تأثيرات الماوس */
        .interactive-bg {
            position: fixed;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255,107,107,0.1) 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: -1;
            transition: all 0.1s ease;
        }
    </style>
</head>
<body>
    <div class="interactive-bg" id="mouseBg"></div>
    
    <div class="calculator-container">
        <div class="calculator-header">
            <h1>🚀 آلة حاسبة ذكية متطورة</h1>
        </div>

        

        <form method="POST" action="">
            <div class="form-group">
                <label for="num1">🔸 الرقم الأول:</label>
                <input type="number" 
                       id="num1" 
                       name="num1" 
                       step="any" 
                       placeholder="أدخل الرقم الأول"
                       value="<?php echo isset($_POST['num1']) ? htmlspecialchars($_POST['num1']) : ''; ?>" 
                       required>
            </div>

            <div class="form-group">
                <label for="operation">⚙️ اختر العملية الحسابية:</label>
                <select id="operation" name="operation" required>
                    <option value="">-- اختر نوع العملية --</option>
                    <option value="add" <?php echo (isset($_POST['operation']) && $_POST['operation'] == 'add') ? 'selected' : ''; ?>>
                        ➕ الجمع - Addition (+)
                    </option>
                    <option value="subtract" <?php echo (isset($_POST['operation']) && $_POST['operation'] == 'subtract') ? 'selected' : ''; ?>>
                        ➖ الطرح - Subtraction (-)
                    </option>
                    <option value="multiply" <?php echo (isset($_POST['operation']) && $_POST['operation'] == 'multiply') ? 'selected' : ''; ?>>
                        ✖️ الضرب - Multiplication (×)
                    </option>
                    <option value="divide" <?php echo (isset($_POST['operation']) && $_POST['operation'] == 'divide') ? 'selected' : ''; ?>>
                        ➗ القسمة - Division (÷)
                    </option>
                    <option value="modulus" <?php echo (isset($_POST['operation']) && $_POST['operation'] == 'modulus') ? 'selected' : ''; ?>>
                        📐 باقي القسمة - Modulus (%)
                    </option>
                    <option value="exponentiation" <?php echo (isset($_POST['operation']) && $_POST['operation'] == 'exponentiation') ? 'selected' : ''; ?>>
                        🔢 الأس - Exponentiation (^)
                    </option>
                </select>
            </div>

            <div class="form-group">
                <label for="num2">🔹 الرقم الثاني:</label>
                <input type="number" 
                       id="num2" 
                       name="num2" 
                       step="any" 
                       placeholder="أدخل الرقم الثاني"
                       value="<?php echo isset($_POST['num2']) ? htmlspecialchars($_POST['num2']) : ''; ?>" 
                       required>
            </div>

            <button type="submit" class="calculate-btn">
                🧮 احسب النتيجة الآن
            </button>
        </form>

        <?php
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            if (isset($_POST['num1']) && isset($_POST['num2']) && isset($_POST['operation'])) {
                $num1 = intval($_POST['num1']);
                $num2 = intval($_POST['num2']);
                $operation = $_POST['operation'];
                $result = null;
                $error = null;
                $operationSymbol = '';
                $operationName = '';
                $operationDescription = '';

                switch ($operation) {
                    case 'add':
                        $result =intval( $num1 + $num2);
                        $operationSymbol = '+';
                        $operationName = 'الجمع';
                        $operationDescription = 'عملية إضافة الأرقام';
                        break;
                    
                    case 'subtract':
                        $result = intval($num1 - $num2);
                        $operationSymbol = '-';
                        $operationName = 'الطرح';
                        $operationDescription = 'عملية طرح الأرقام';
                        break;
                    
                    case 'multiply':
                        $result = intval($num1 * $num2);
                        $operationSymbol = '×';
                        $operationName = 'الضرب';
                        $operationDescription = 'عملية ضرب الأرقام';
                        break;
                    
                    case 'divide':
                        if ($num2 != 0) {
                            $result = intval($num1 / $num2);
                            $operationSymbol = '÷';
                            $operationName = 'القسمة';
                            $operationDescription = 'عملية قسمة الأرقام';
                        } else {
                            $error = "🚫 خطأ رياضي: لا يمكن القسمة على صفر!";
                        }
                        break;
                    
                    case 'modulus':
                        if ($num2 != 0) {
                            $result = fmod($num1, $num2);
                            $operationSymbol = '%';
                            $operationName = 'باقي القسمة';
                            $operationDescription = 'حساب الباقي بعد القسمة';
                        } else {
                            $error = "🚫 خطأ رياضي: لا يمكن حساب باقي القسمة على صفر!";
                        }
                        break;
                    
                    case 'exponentiation':
                        $result = pow($num1, $num2);
                        $operationSymbol = '^';
                        $operationName = 'الأس';
                        $operationDescription = 'رفع الرقم الأول إلى قوة الرقم الثاني';
                        break;
                    
                    default:
                        $error = "⚠️ خطأ في النظام: عملية غير معروفة!";
                        break;
                }

                if ($error) {
                    echo '<div class="error">' . $error . '</div>';
                } else {
                    // تحديد نوع الرقم مع تفاصيل أكثر
                    $numberType = '';
                    $numberIcon = '';
                    
                    if (is_numeric($result)) {
                        if ($result == intval($result)) {
                            $intResult = intval($result);
                            if ($intResult == 0) {
                                $numberType = "⚪ الرقم صفر (Zero)";
                                $numberIcon = "⚪";
                            } elseif ($intResult > 0) {
                                if ($intResult % 2 == 0) {
                                    $numberType = "🟢 رقم صحيح موجب زوجي (Positive Even Integer)";
                                    $numberIcon = "🟢";
                                } else {
                                    $numberType = "🔵 رقم صحيح موجب فردي (Positive Odd Integer)";
                                    $numberIcon = "🔵";
                                }
                            } else {
                                if ($intResult % 2 == 0) {
                                    $numberType = "🟠 رقم صحيح سالب زوجي (Negative Even Integer)";
                                    $numberIcon = "🟠";
                                } else {
                                    $numberType = "🔴 رقم صحيح سالب فردي (Negative Odd Integer)";
                                    $numberIcon = "🔴";
                                }
                            }
                        } else {
                            if ($result > 0) {
                                $numberType = "🟡 رقم عشري موجب (Positive Decimal)";
                                $numberIcon = "🟡";
                            } else {
                                $numberType = "🟤 رقم عشري سالب (Negative Decimal)";
                                $numberIcon = "🟤";
                            }
                        }
                    }

                    echo '<div class="result">';
                    echo '<h3>🎉 نتيجة العملية الحسابية</h3>';
                    echo '<p><strong>نوع العملية:</strong> ' . $operationName . ' (' . $operationDescription . ')</p>';
                    echo '<div class="operation-display">' . number_format($num1, 2) . ' ' . $operationSymbol . ' ' . number_format($num2, 2) . ' = ' . rtrim(rtrim(number_format($result, 8, '.', ''), '0'), '.') . '</div>';
                    echo '<p><strong>النتيجة النهائية:</strong> ' . rtrim(rtrim(number_format($result, 8, '.', ''), '0'), '.') . '</p>';
                    echo '<div class="number-type">' . $numberIcon . ' ' . $numberType . '</div>';
                    echo '</div>';
                }
            }
        }
        ?>
    </div>

    
</body>
</html>