<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>آلة حاسبة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #2c3e50;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .calculator-container {
            background: #34495e;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            width: 100%;
            max-width: 500px;
        }

        .calculator-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .calculator-header h1 {
            color: #ecf0f1;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #ecf0f1;
            font-weight: bold;
        }

        input[type="number"], select {
            width: 100%;
            padding: 12px;
            border: 1px solid #7f8c8d;
            border-radius: 5px;
            font-size: 16px;
            background: #ecf0f1;
            color: #2c3e50;
        }

        input[type="number"]:focus, select:focus {
            outline: none;
            border-color: #3498db;
        }

        select {
            cursor: pointer;
        }

        .calculate-btn {
            width: 100%;
            padding: 15px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            margin-top: 10px;
        }

        .calculate-btn:hover {
            background: #2980b9;
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            background: #27ae60;
            border-radius: 5px;
            text-align: center;
            color: white;
        }

        .result h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
        }

        .result p {
            font-size: 1.1em;
            margin: 8px 0;
        }

        .operation-display {
            background: #2c3e50;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-size: 1.3em;
            font-weight: bold;
            color: #ecf0f1;
        }

        .number-type {
            background: #16a085;
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
            font-weight: bold;
            color: white;
        }

        .error {
            background: #e74c3c;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .calculator-container {
                margin: 10px;
                padding: 20px;
            }

            .calculator-header h1 {
                font-size: 1.8em;
            }
        }
    </style>
</head>
<body>
    <div class="calculator-container">
        <div class="calculator-header">
            <h1>آلة حاسبة</h1>
        </div>

        <form method="POST" action="">
            <div class="form-group">
                <label for="num1">الرقم الأول:</label>
                <input type="number"
                       id="num1"
                       name="num1"
                       step="any"
                       placeholder="أدخل الرقم الأول"
                       value="<?php echo isset($_POST['num1']) ? htmlspecialchars($_POST['num1']) : ''; ?>"
                       required>
            </div>

            <div class="form-group">
                <label for="operation">اختر العملية الحسابية:</label>
                <select id="operation" name="operation" required>
                    <option value="">-- اختر نوع العملية --</option>
                    <option value="add" <?php echo (isset($_POST['operation']) && $_POST['operation'] == 'add') ? 'selected' : ''; ?>>
                        الجمع (+)
                    </option>
                    <option value="subtract" <?php echo (isset($_POST['operation']) && $_POST['operation'] == 'subtract') ? 'selected' : ''; ?>>
                        الطرح (-)
                    </option>
                    <option value="multiply" <?php echo (isset($_POST['operation']) && $_POST['operation'] == 'multiply') ? 'selected' : ''; ?>>
                        الضرب (×)
                    </option>
                    <option value="divide" <?php echo (isset($_POST['operation']) && $_POST['operation'] == 'divide') ? 'selected' : ''; ?>>
                        القسمة (÷)
                    </option>
                    <option value="modulus" <?php echo (isset($_POST['operation']) && $_POST['operation'] == 'modulus') ? 'selected' : ''; ?>>
                        باقي القسمة (%)
                    </option>
                    <option value="exponentiation" <?php echo (isset($_POST['operation']) && $_POST['operation'] == 'exponentiation') ? 'selected' : ''; ?>>
                        الأس (^)
                    </option>
                </select>
            </div>

            <div class="form-group">
                <label for="num2">الرقم الثاني:</label>
                <input type="number"
                       id="num2"
                       name="num2"
                       step="any"
                       placeholder="أدخل الرقم الثاني"
                       value="<?php echo isset($_POST['num2']) ? htmlspecialchars($_POST['num2']) : ''; ?>"
                       required>
            </div>

            <button type="submit" class="calculate-btn">
                احسب النتيجة
            </button>
        </form>

        <?php
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            if (isset($_POST['num1']) && isset($_POST['num2']) && isset($_POST['operation'])) {
                $num1 = floatval($_POST['num1']);
                $num2 = floatval($_POST['num2']);
                $operation = $_POST['operation'];
                $result = null;
                $error = null;
                $operationSymbol = '';

                switch ($operation) {
                    case 'add':
                        $result = $num1 + $num2;
                        $operationSymbol = '+';
                        break;

                    case 'subtract':
                        $result = $num1 - $num2;
                        $operationSymbol = '-';
                        break;

                    case 'multiply':
                        $result = $num1 * $num2;
                        $operationSymbol = '×';
                        break;

                    case 'divide':
                        if ($num2 != 0) {
                            $result = $num1 / $num2;
                            $operationSymbol = '÷';
                        } else {
                            $error = "خطأ: لا يمكن القسمة على صفر";
                        }
                        break;

                    case 'modulus':
                        if ($num2 != 0) {
                            $result = fmod($num1, $num2);
                            $operationSymbol = '%';
                        } else {
                            $error = "خطأ: لا يمكن حساب باقي القسمة على صفر";
                        }
                        break;

                    case 'exponentiation':
                        $result = pow($num1, $num2);
                        $operationSymbol = '^';
                        break;

                    default:
                        $error = "خطأ: عملية غير معروفة";
                        break;
                }

                if ($error) {
                    echo '<div class="error">' . $error . '</div>';
                } else {
                    echo '<div class="result">';
                    echo '<h3>نتيجة العملية</h3>';
                    echo '<div class="operation-display">' . $num1 . ' ' . $operationSymbol . ' ' . $num2 . ' = ' . $result . '</div>';
                    echo '<p><strong>النتيجة:</strong> ' . $result . '</p>';
                    echo '</div>';
                }
            }
        }
        ?>
    </div>


</body>
</html>